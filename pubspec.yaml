name: medical_app
description: "تطبيق طبي متكامل واحترافي"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.4
  equatable: ^2.0.5

  # Firebase
  firebase_core: ^3.14.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.3
  firebase_messaging: ^15.1.3

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # Navigation & Routing
  go_router: ^14.6.2

  # HTTP & API
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # Local Storage
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Date & Time
  intl: ^0.19.0
  table_calendar: ^3.0.9

  # Image & Camera
  image_picker: ^1.1.2
  image_cropper: ^5.0.1
  permission_handler: ^11.3.1

  # Payment
  stripe_platform_interface: ^9.1.0
  pay: ^1.1.2

  # AI & ML
  tflite_flutter: ^0.10.4
  camera: ^0.10.5+9

  # Notifications
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4

  # Charts & Analytics
  fl_chart: ^0.68.0

  # QR Code
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1

  # Video Call
  agora_rtc_engine: ^6.3.2

  # File & Document
  file_picker: ^6.2.0
  path_provider: ^2.1.4

  # Utils
  uuid: ^4.4.0
  url_launcher: ^6.2.5
  package_info_plus: 8.3.0
  device_info_plus: ^10.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  
  # Code Generation
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
  retrofit_generator: ^8.1.0
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300 