import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends Equatable {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String userType; // patient, doctor, admin
  final DateTime dateOfBirth;
  final String? gender;
  final String? address;
  final String? city;
  final String? country;
  final String? emergencyContact;
  final String? emergencyPhone;
  final List<String>? allergies;
  final List<String>? chronicConditions;
  final List<String>? medications;
  final String? bloodType;
  final double? height; // in cm
  final double? weight; // in kg
  final String subscriptionPlan;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isProfileComplete;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  final Map<String, dynamic>? preferences;
  final List<String>? fcmTokens;

  const UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.userType,
    required this.dateOfBirth,
    this.gender,
    this.address,
    this.city,
    this.country,
    this.emergencyContact,
    this.emergencyPhone,
    this.allergies,
    this.chronicConditions,
    this.medications,
    this.bloodType,
    this.height,
    this.weight,
    this.subscriptionPlan = 'free',
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.isProfileComplete = false,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.isActive = true,
    this.preferences,
    this.fcmTokens,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromJson({
      'id': doc.id,
      ...data,
    });
  }

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id');
    return json;
  }

  String get fullName => '$firstName $lastName';
  
  String get displayName => '$firstName $lastName';
  
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  double? get bmi {
    if (height != null && weight != null && height! > 0) {
      final heightInMeters = height! / 100;
      return weight! / (heightInMeters * heightInMeters);
    }
    return null;
  }

  String? get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return null;
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  bool get isDoctor => userType == 'doctor';
  bool get isPatient => userType == 'patient';
  bool get isAdmin => userType == 'admin';

  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    String? userType,
    DateTime? dateOfBirth,
    String? gender,
    String? address,
    String? city,
    String? country,
    String? emergencyContact,
    String? emergencyPhone,
    List<String>? allergies,
    List<String>? chronicConditions,
    List<String>? medications,
    String? bloodType,
    double? height,
    double? weight,
    String? subscriptionPlan,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isProfileComplete,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    bool? isActive,
    Map<String, dynamic>? preferences,
    List<String>? fcmTokens,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      userType: userType ?? this.userType,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      allergies: allergies ?? this.allergies,
      chronicConditions: chronicConditions ?? this.chronicConditions,
      medications: medications ?? this.medications,
      bloodType: bloodType ?? this.bloodType,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      preferences: preferences ?? this.preferences,
      fcmTokens: fcmTokens ?? this.fcmTokens,
    );
  }

  @override
  List<Object?> get props => [
    id,
    email,
    firstName,
    lastName,
    phoneNumber,
    profileImageUrl,
    userType,
    dateOfBirth,
    gender,
    address,
    city,
    country,
    emergencyContact,
    emergencyPhone,
    allergies,
    chronicConditions,
    medications,
    bloodType,
    height,
    weight,
    subscriptionPlan,
    isEmailVerified,
    isPhoneVerified,
    isProfileComplete,
    createdAt,
    updatedAt,
    lastLoginAt,
    isActive,
    preferences,
    fcmTokens,
  ];
} 