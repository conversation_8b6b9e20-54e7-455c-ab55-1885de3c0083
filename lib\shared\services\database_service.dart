import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../../core/constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/doctor_model.dart';
import '../models/appointment_model.dart';

class DatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Generic CRUD operations
  Future<void> createDocument(String collection, String documentId, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(documentId).set(data);
    } catch (e) {
      throw 'فشل في إنشاء المستند: $e';
    }
  }

  Future<Map<String, dynamic>?> getDocument(String collection, String documentId) async {
    try {
      final doc = await _firestore.collection(collection).doc(documentId).get();
      if (doc.exists) {
        return doc.data() as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      throw 'فشل في جلب المستند: $e';
    }
  }

  Future<void> updateDocument(String collection, String documentId, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(documentId).update(data);
    } catch (e) {
      throw 'فشل في تحديث المستند: $e';
    }
  }

  Future<void> deleteDocument(String collection, String documentId) async {
    try {
      await _firestore.collection(collection).doc(documentId).delete();
    } catch (e) {
      throw 'فشل في حذف المستند: $e';
    }
  }

  // Query operations
  Future<List<Map<String, dynamic>>> queryDocuments(
    String collection, {
    String? field,
    dynamic isEqualTo,
    dynamic isGreaterThan,
    dynamic isLessThan,
    int? limit,
    String? orderBy,
    bool descending = false,
  }) async {
    try {
      Query query = _firestore.collection(collection);

      if (field != null && isEqualTo != null) {
        query = query.where(field, isEqualTo: isEqualTo);
      }

      if (field != null && isGreaterThan != null) {
        query = query.where(field, isGreaterThan: isGreaterThan);
      }

      if (field != null && isLessThan != null) {
        query = query.where(field, isLessThan: isLessThan);
      }

      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs.map((doc) => doc.data() as Map<String, dynamic>).toList();
    } catch (e) {
      throw 'فشل في استعلام المستندات: $e';
    }
  }

  // Stream operations
  Stream<List<Map<String, dynamic>>> streamCollection(
    String collection, {
    String? field,
    dynamic isEqualTo,
    int? limit,
    String? orderBy,
    bool descending = false,
  }) {
    Query query = _firestore.collection(collection);

    if (field != null && isEqualTo != null) {
      query = query.where(field, isEqualTo: isEqualTo);
    }

    if (orderBy != null) {
      query = query.orderBy(orderBy, descending: descending);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => doc.data() as Map<String, dynamic>).toList();
    });
  }

  // User operations
  Future<void> createUser(UserModel user) async {
    await createDocument(AppConstants.usersCollection, user.id, user.toFirestore());
  }

  Future<UserModel?> getUser(String userId) async {
    final data = await getDocument(AppConstants.usersCollection, userId);
    if (data != null) {
      return UserModel.fromJson({...data, 'id': userId});
    }
    return null;
  }

  Future<void> updateUser(String userId, Map<String, dynamic> data) async {
    await updateDocument(AppConstants.usersCollection, userId, data);
  }

  Stream<UserModel?> streamUser(String userId) {
    return _firestore
        .collection(AppConstants.usersCollection)
        .doc(userId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    });
  }

  // Doctor operations
  Future<void> createDoctor(DoctorModel doctor) async {
    await createDocument(AppConstants.doctorsCollection, doctor.id, doctor.toJson());
  }

  Future<DoctorModel?> getDoctor(String doctorId) async {
    final data = await getDocument(AppConstants.doctorsCollection, doctorId);
    if (data != null) {
      return DoctorModel.fromJson({...data, 'id': doctorId});
    }
    return null;
  }

  Future<List<DoctorModel>> getDoctors({
    String? specialization,
    bool? isAvailable,
    bool? isVerified,
    int? limit,
  }) async {
    List<Map<String, dynamic>> query = [];
    
    if (specialization != null) {
      query = await queryDocuments(
        AppConstants.doctorsCollection,
        field: 'specialization',
        isEqualTo: specialization,
        limit: limit,
      );
    } else if (isAvailable != null) {
      query = await queryDocuments(
        AppConstants.doctorsCollection,
        field: 'isAvailable',
        isEqualTo: isAvailable,
        limit: limit,
      );
    } else if (isVerified != null) {
      query = await queryDocuments(
        AppConstants.doctorsCollection,
        field: 'isVerified',
        isEqualTo: isVerified,
        limit: limit,
      );
    } else {
      query = await queryDocuments(
        AppConstants.doctorsCollection,
        limit: limit,
      );
    }

    return query.map((data) => DoctorModel.fromJson(data)).toList();
  }

  Stream<List<DoctorModel>> streamDoctors({
    String? specialization,
    bool? isAvailable,
    bool? isVerified,
    int? limit,
  }) {
    Query query = _firestore.collection(AppConstants.doctorsCollection);

    if (specialization != null) {
      query = query.where('specialization', isEqualTo: specialization);
    }

    if (isAvailable != null) {
      query = query.where('isAvailable', isEqualTo: isAvailable);
    }

    if (isVerified != null) {
      query = query.where('isVerified', isEqualTo: isVerified);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => DoctorModel.fromJson(doc.data() as Map<String, dynamic>)).toList();
    });
  }

  // Appointment operations
  Future<void> createAppointment(AppointmentModel appointment) async {
    await createDocument(AppConstants.appointmentsCollection, appointment.id, appointment.toJson());
  }

  Future<AppointmentModel?> getAppointment(String appointmentId) async {
    final data = await getDocument(AppConstants.appointmentsCollection, appointmentId);
    if (data != null) {
      return AppointmentModel.fromJson({...data, 'id': appointmentId});
    }
    return null;
  }

  Future<List<AppointmentModel>> getAppointments({
    String? patientId,
    String? doctorId,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    List<Map<String, dynamic>> query = [];

    if (patientId != null) {
      query = await queryDocuments(
        AppConstants.appointmentsCollection,
        field: 'patientId',
        isEqualTo: patientId,
        orderBy: 'appointmentDate',
        descending: true,
        limit: limit,
      );
    } else if (doctorId != null) {
      query = await queryDocuments(
        AppConstants.appointmentsCollection,
        field: 'doctorId',
        isEqualTo: doctorId,
        orderBy: 'appointmentDate',
        descending: true,
        limit: limit,
      );
    } else if (status != null) {
      query = await queryDocuments(
        AppConstants.appointmentsCollection,
        field: 'status',
        isEqualTo: status,
        orderBy: 'appointmentDate',
        descending: true,
        limit: limit,
      );
    } else {
      query = await queryDocuments(
        AppConstants.appointmentsCollection,
        orderBy: 'appointmentDate',
        descending: true,
        limit: limit,
      );
    }

    return query.map((data) => AppointmentModel.fromJson(data)).toList();
  }

  Stream<List<AppointmentModel>> streamAppointments({
    String? patientId,
    String? doctorId,
    String? status,
    int? limit,
  }) {
    Query query = _firestore.collection(AppConstants.appointmentsCollection);

    if (patientId != null) {
      query = query.where('patientId', isEqualTo: patientId);
    }

    if (doctorId != null) {
      query = query.where('doctorId', isEqualTo: doctorId);
    }

    if (status != null) {
      query = query.where('status', isEqualTo: status);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    query = query.orderBy('appointmentDate', descending: true);

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => AppointmentModel.fromJson(doc.data() as Map<String, dynamic>)).toList();
    });
  }

  // File upload operations
  Future<String> uploadFile(File file, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw 'فشل في رفع الملف: $e';
    }
  }

  Future<void> deleteFile(String url) async {
    try {
      final ref = _storage.refFromURL(url);
      await ref.delete();
    } catch (e) {
      throw 'فشل في حذف الملف: $e';
    }
  }

  // Batch operations
  Future<void> batchWrite(List<Map<String, dynamic>> operations) async {
    try {
      final batch = _firestore.batch();
      
      for (final operation in operations) {
        final collection = operation['collection'] as String;
        final documentId = operation['documentId'] as String;
        final data = operation['data'] as Map<String, dynamic>;
        final type = operation['type'] as String;

        final docRef = _firestore.collection(collection).doc(documentId);

        switch (type) {
          case 'create':
            batch.set(docRef, data);
            break;
          case 'update':
            batch.update(docRef, data);
            break;
          case 'delete':
            batch.delete(docRef);
            break;
        }
      }

      await batch.commit();
    } catch (e) {
      throw 'فشل في عملية الدفعة: $e';
    }
  }

  // Transaction operations
  Future<T> runTransaction<T>(Future<T> Function(Transaction) transaction) async {
    try {
      return await _firestore.runTransaction(transaction);
    } catch (e) {
      throw 'فشل في العملية: $e';
    }
  }
} 