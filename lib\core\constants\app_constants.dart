class AppConstants {
  // App Information
  static const String appName = 'MediCare';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق طبي متكامل واحترافي';
  
  // API Endpoints
  static const String baseUrl = 'https://api.medicare.com';
  static const String apiVersion = '/v1';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String doctorsCollection = 'doctors';
  static const String appointmentsCollection = 'appointments';
  static const String medicalRecordsCollection = 'medical_records';
  static const String prescriptionsCollection = 'prescriptions';
  static const String articlesCollection = 'articles';
  static const String symptomsCollection = 'symptoms';
  static const String chatMessagesCollection = 'chat_messages';
  static const String notificationsCollection = 'notifications';
  
  // Storage Paths
  static const String profileImagesPath = 'profile_images';
  static const String medicalDocumentsPath = 'medical_documents';
  static const String prescriptionImagesPath = 'prescription_images';
  
  // User Types
  static const String userTypePatient = 'patient';
  static const String userTypeDoctor = 'doctor';
  static const String userTypeAdmin = 'admin';
  
  // Appointment Status
  static const String appointmentStatusPending = 'pending';
  static const String appointmentStatusConfirmed = 'confirmed';
  static const String appointmentStatusCancelled = 'cancelled';
  static const String appointmentStatusCompleted = 'completed';
  
  // Payment Methods
  static const String paymentMethodCard = 'card';
  static const String paymentMethodCash = 'cash';
  static const String paymentMethodInsurance = 'insurance';
  
  // Subscription Plans
  static const String planFree = 'free';
  static const String planBasic = 'basic';
  static const String planPremium = 'premium';
  static const String planEnterprise = 'enterprise';
  
  // Notification Types
  static const String notificationTypeAppointment = 'appointment';
  static const String notificationTypeMessage = 'message';
  static const String notificationTypeReminder = 'reminder';
  static const String notificationTypeSystem = 'system';
  
  // Chat Types
  static const String chatTypeDirect = 'direct';
  static const String chatTypeGroup = 'group';
  static const String chatTypeSupport = 'support';
  
  // File Types
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Cache Duration
  static const int cacheDuration = 3600; // 1 hour
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  
  // Limits
  static const int maxAppointmentsPerDay = 10;
  static const int maxChatMessages = 1000;
  static const int maxMedicalRecords = 100;
  
  // AI Services
  static const String openaiApiKey = 'YOUR_OPENAI_API_KEY';
  static const String symptomCheckerModel = 'gpt-4';
  static const String medicalChatModel = 'gpt-4';
  
  // Payment Gateway
  static const String stripePublishableKey = 'YOUR_STRIPE_PUBLISHABLE_KEY';
  static const String stripeSecretKey = 'YOUR_STRIPE_SECRET_KEY';
  
  // Video Call
  static const String agoraAppId = 'YOUR_AGORA_APP_ID';
  static const String agoraAppCertificate = 'YOUR_AGORA_APP_CERTIFICATE';
  
  // Social Media
  static const String facebookUrl = 'https://facebook.com/medicare';
  static const String twitterUrl = 'https://twitter.com/medicare';
  static const String instagramUrl = 'https://instagram.com/medicare';
  static const String linkedinUrl = 'https://linkedin.com/company/medicare';
  
  // Support
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+966-50-123-4567';
  static const String supportWhatsapp = '+966-50-123-4567';
  
  // Legal
  static const String privacyPolicyUrl = 'https://medicare.com/privacy';
  static const String termsOfServiceUrl = 'https://medicare.com/terms';
  static const String medicalDisclaimerUrl = 'https://medicare.com/disclaimer';
} 