import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'appointment_model.g.dart';

@JsonSerializable()
class AppointmentModel extends Equatable {
  final String id;
  final String patientId;
  final String doctorId;
  final String? patientName;
  final String? doctorName;
  final String? doctorSpecialization;
  final DateTime appointmentDate;
  final String appointmentTime;
  final int duration; // in minutes
  final String appointmentType; // consultation, follow_up, emergency
  final String status; // pending, confirmed, cancelled, completed
  final String? reason;
  final String? symptoms;
  final String? notes;
  final String? prescription;
  final String? diagnosis;
  final String? treatment;
  final String? recommendations;
  final double? consultationFee;
  final String paymentMethod;
  final String paymentStatus; // pending, paid, refunded
  final String? paymentId;
  final String? meetingLink;
  final String? meetingPassword;
  final bool isOnline;
  final bool isEmergency;
  final bool isFollowUp;
  final String? previousAppointmentId;
  final List<String>? attachments;
  final Map<String, dynamic>? vitalSigns;
  final String? rating;
  final String? review;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? cancelledAt;
  final String? cancelledBy;
  final String? cancelReason;

  const AppointmentModel({
    required this.id,
    required this.patientId,
    required this.doctorId,
    this.patientName,
    this.doctorName,
    this.doctorSpecialization,
    required this.appointmentDate,
    required this.appointmentTime,
    this.duration = 30,
    required this.appointmentType,
    required this.status,
    this.reason,
    this.symptoms,
    this.notes,
    this.prescription,
    this.diagnosis,
    this.treatment,
    this.recommendations,
    this.consultationFee,
    this.paymentMethod = 'card',
    this.paymentStatus = 'pending',
    this.paymentId,
    this.meetingLink,
    this.meetingPassword,
    this.isOnline = false,
    this.isEmergency = false,
    this.isFollowUp = false,
    this.previousAppointmentId,
    this.attachments,
    this.vitalSigns,
    this.rating,
    this.review,
    required this.createdAt,
    required this.updatedAt,
    this.cancelledAt,
    this.cancelledBy,
    this.cancelReason,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) => _$AppointmentModelFromJson(json);
  Map<String, dynamic> toJson() => _$AppointmentModelToJson(this);

  DateTime get appointmentDateTime {
    final timeParts = appointmentTime.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    return DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
      hour,
      minute,
    );
  }

  DateTime get endTime {
    return appointmentDateTime.add(Duration(minutes: duration));
  }

  bool get isToday {
    final now = DateTime.now();
    return appointmentDate.year == now.year &&
           appointmentDate.month == now.month &&
           appointmentDate.day == now.day;
  }

  bool get isPast {
    return appointmentDateTime.isBefore(DateTime.now());
  }

  bool get isUpcoming {
    return appointmentDateTime.isAfter(DateTime.now());
  }

  bool get canBeCancelled {
    final now = DateTime.now();
    final difference = appointmentDateTime.difference(now);
    return difference.inHours >= 24 && status == 'confirmed';
  }

  bool get canBeRescheduled {
    final now = DateTime.now();
    final difference = appointmentDateTime.difference(now);
    return difference.inHours >= 2 && status == 'confirmed';
  }

  String get statusText {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'cancelled':
        return 'ملغي';
      case 'completed':
        return 'مكتمل';
      default:
        return 'غير معروف';
    }
  }

  String get appointmentTypeText {
    switch (appointmentType) {
      case 'consultation':
        return 'استشارة';
      case 'follow_up':
        return 'متابعة';
      case 'emergency':
        return 'طوارئ';
      default:
        return 'غير معروف';
    }
  }

  String get paymentStatusText {
    switch (paymentStatus) {
      case 'pending':
        return 'في الانتظار';
      case 'paid':
        return 'مدفوع';
      case 'refunded':
        return 'مسترد';
      default:
        return 'غير معروف';
    }
  }

  AppointmentModel copyWith({
    String? id,
    String? patientId,
    String? doctorId,
    String? patientName,
    String? doctorName,
    String? doctorSpecialization,
    DateTime? appointmentDate,
    String? appointmentTime,
    int? duration,
    String? appointmentType,
    String? status,
    String? reason,
    String? symptoms,
    String? notes,
    String? prescription,
    String? diagnosis,
    String? treatment,
    String? recommendations,
    double? consultationFee,
    String? paymentMethod,
    String? paymentStatus,
    String? paymentId,
    String? meetingLink,
    String? meetingPassword,
    bool? isOnline,
    bool? isEmergency,
    bool? isFollowUp,
    String? previousAppointmentId,
    List<String>? attachments,
    Map<String, dynamic>? vitalSigns,
    String? rating,
    String? review,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? cancelledAt,
    String? cancelledBy,
    String? cancelReason,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      doctorId: doctorId ?? this.doctorId,
      patientName: patientName ?? this.patientName,
      doctorName: doctorName ?? this.doctorName,
      doctorSpecialization: doctorSpecialization ?? this.doctorSpecialization,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      duration: duration ?? this.duration,
      appointmentType: appointmentType ?? this.appointmentType,
      status: status ?? this.status,
      reason: reason ?? this.reason,
      symptoms: symptoms ?? this.symptoms,
      notes: notes ?? this.notes,
      prescription: prescription ?? this.prescription,
      diagnosis: diagnosis ?? this.diagnosis,
      treatment: treatment ?? this.treatment,
      recommendations: recommendations ?? this.recommendations,
      consultationFee: consultationFee ?? this.consultationFee,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentId: paymentId ?? this.paymentId,
      meetingLink: meetingLink ?? this.meetingLink,
      meetingPassword: meetingPassword ?? this.meetingPassword,
      isOnline: isOnline ?? this.isOnline,
      isEmergency: isEmergency ?? this.isEmergency,
      isFollowUp: isFollowUp ?? this.isFollowUp,
      previousAppointmentId: previousAppointmentId ?? this.previousAppointmentId,
      attachments: attachments ?? this.attachments,
      vitalSigns: vitalSigns ?? this.vitalSigns,
      rating: rating ?? this.rating,
      review: review ?? this.review,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancelledBy: cancelledBy ?? this.cancelledBy,
      cancelReason: cancelReason ?? this.cancelReason,
    );
  }

  @override
  List<Object?> get props => [
    id,
    patientId,
    doctorId,
    patientName,
    doctorName,
    doctorSpecialization,
    appointmentDate,
    appointmentTime,
    duration,
    appointmentType,
    status,
    reason,
    symptoms,
    notes,
    prescription,
    diagnosis,
    treatment,
    recommendations,
    consultationFee,
    paymentMethod,
    paymentStatus,
    paymentId,
    meetingLink,
    meetingPassword,
    isOnline,
    isEmergency,
    isFollowUp,
    previousAppointmentId,
    attachments,
    vitalSigns,
    rating,
    review,
    createdAt,
    updatedAt,
    cancelledAt,
    cancelledBy,
    cancelReason,
  ];
} 