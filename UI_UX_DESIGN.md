# دليل تصميم UI/UX - MediCare

## 🎨 فلسفة التصميم

### المبادئ الأساسية
- **البساطة**: واجهات نظيفة وسهلة الاستخدام
- **الوضوح**: معلومات واضحة ومفهومة
- **السهولة**: تجربة مستخدم سلسة
- **الموثوقية**: تصميم يبعث على الثقة
- **الشمولية**: قابلية الوصول للجميع

### الهوية البصرية
- **الألوان الرئيسية**: أزرق طبي (#2563EB)
- **الألوان الثانوية**: أخضر صحي (#10B981)
- **الألوان المساعدة**: برتقالي تحذيري (#F59E0B)
- **الخطوط**: Cairo (عربي) و Inter (إنجليزي)

## 📱 تصميم الواجهات

### 1. الشاشة الرئيسية (Home Screen)

#### التخطيط العام
```
┌─────────────────────────────────┐
│ Header (Gradient Background)    │
│ ├─ Welcome Message              │
│ ├─ Search Bar                   │
│ └─ Profile Avatar               │
├─────────────────────────────────┤
│ Quick Actions Grid (2x2)        │
│ ├─ Book Appointment             │
│ ├─ Symptom Checker              │
│ ├─ Medical Encyclopedia         │
│ └─ Medical Chat                 │
├─────────────────────────────────┤
│ Upcoming Appointments           │
│ └─ Appointment Cards            │
├─────────────────────────────────┤
│ Recommended Doctors             │
│ └─ Doctor Cards (Horizontal)    │
├─────────────────────────────────┤
│ Medical Services                │
│ └─ Service List                 │
└─────────────────────────────────┘
```

#### العناصر التفاعلية
- **البطاقات**: ظلال خفيفة وحواف مدورة
- **الأزرار**: تدرج لوني مع تأثيرات النقر
- **الحقول**: حدود واضحة مع مؤشرات الحالة

### 2. شاشة حجز المواعيد (Appointment Booking)

#### خطوات الحجز
1. **اختيار التخصص**
   - قائمة التخصصات مع أيقونات
   - بحث سريع
   - التخصصات المفضلة

2. **اختيار الطبيب**
   - صور الأطباء
   - التقييمات والمراجعات
   - معلومات الخبرة
   - الرسوم

3. **اختيار التاريخ والوقت**
   - تقويم تفاعلي
   - الأوقات المتاحة
   - مدة الموعد

4. **تأكيد الحجز**
   - ملخص الموعد
   - طريقة الدفع
   - الشروط والأحكام

#### التصميم التفاعلي
- **التقدم**: شريط تقدم في الأعلى
- **الانتقال**: أزرار "التالي" و "السابق"
- **الحفظ**: حفظ البيانات تلقائياً

### 3. شاشة فحص الأعراض (Symptom Checker)

#### واجهة التشخيص
```
┌─────────────────────────────────┐
│ AI Chat Interface               │
│ ├─ Welcome Message              │
│ ├─ Symptom Input                │
│ ├─ Question Flow                │
│ └─ Diagnosis Results            │
├─────────────────────────────────┤
│ Symptom Categories              │
│ ├─ Body Parts                   │
│ ├─ Common Symptoms              │
│ └─ Emergency Symptoms           │
├─────────────────────────────────┤
│ Quick Actions                   │
│ ├─ Book Doctor                  │
│ ├─ Emergency Call               │
│ └─ Save Results                 │
└─────────────────────────────────┘
```

#### تجربة المستخدم
- **المحادثة الطبيعية**: واجهة دردشة سهلة
- **الإرشادات البصرية**: رسوم توضيحية للأعراض
- **النتائج الفورية**: تشخيص سريع وواضح

### 4. شاشة الدردشة الطبية (Medical Chat)

#### واجهة المحادثة
```
┌─────────────────────────────────┐
│ Chat Header                     │
│ ├─ Doctor Info                  │
│ ├─ Online Status                │
│ └─ Call Button                  │
├─────────────────────────────────┤
│ Messages Area                   │
│ ├─ Text Messages                │
│ ├─ Voice Messages               │
│ ├─ Images                       │
│ └─ Medical Files                │
├─────────────────────────────────┤
│ Input Area                      │
│ ├─ Text Input                   │
│ ├─ Attachment Button            │
│ ├─ Voice Button                 │
│ └─ Send Button                  │
└─────────────────────────────────┘
```

#### المميزات التفاعلية
- **الرسائل الفورية**: تحديث فوري
- **المرفقات**: صور ومستندات طبية
- **التسجيل الصوتي**: رسائل صوتية
- **المكالمات**: انتقال سلس للمكالمات

### 5. شاشة الملف الشخصي (Profile)

#### معلومات المستخدم
```
┌─────────────────────────────────┐
│ Profile Header                  │
│ ├─ Profile Picture              │
│ ├─ Name & Email                 │
│ └─ Edit Button                  │
├─────────────────────────────────┤
│ Personal Information            │
│ ├─ Basic Details                │
│ ├─ Medical History              │
│ ├─ Allergies                    │
│ └─ Medications                  │
├─────────────────────────────────┤
│ Medical Records                 │
│ ├─ Appointments History         │
│ ├─ Prescriptions                │
│ ├─ Test Results                 │
│ └─ Reports                      │
├─────────────────────────────────┤
│ Settings                        │
│ ├─ Notifications                │
│ ├─ Privacy                      │
│ ├─ Language                     │
│ └─ Logout                       │
└─────────────────────────────────┘
```

## 🎯 تجربة المستخدم (UX)

### سير العمل (User Journey)

#### 1. تسجيل الدخول
- **الخطوة 1**: إدخال البريد الإلكتروني
- **الخطوة 2**: إدخال كلمة المرور
- **الخطوة 3**: التحقق من البريد (للمرة الأولى)
- **الخطوة 4**: إكمال الملف الشخصي

#### 2. حجز موعد
- **الخطوة 1**: اختيار التخصص
- **الخطوة 2**: اختيار الطبيب
- **الخطوة 3**: اختيار التاريخ والوقت
- **الخطوة 4**: إدخال الأعراض
- **الخطوة 5**: الدفع
- **الخطوة 6**: التأكيد

#### 3. الاستشارة الطبية
- **الخطوة 1**: الدخول للموعد
- **الخطوة 2**: المكالمة المرئية
- **الخطوة 3**: استشارة الطبيب
- **الخطوة 4**: استلام الوصفة
- **الخطوة 5**: التقييم

### نقاط التفاعل الرئيسية

#### 1. البحث والاكتشاف
- **البحث الذكي**: اقتراحات أثناء الكتابة
- **التصفية**: حسب التخصص والموقع والسعر
- **المفضلة**: حفظ الأطباء المفضلين

#### 2. الحجز والدفع
- **الجدولة الذكية**: اقتراح أوقات مناسبة
- **الدفع الآمن**: طرق دفع متعددة
- **التأكيد**: رسائل تأكيد واضحة

#### 3. التواصل
- **الرسائل**: محادثات فورية
- **المكالمات**: جودة عالية
- **المرفقات**: مشاركة المستندات

## 🎨 نظام الألوان

### الألوان الأساسية
```css
/* Primary Colors */
--primary: #2563EB;
--primary-light: #3B82F6;
--primary-dark: #1D4ED8;

/* Secondary Colors */
--secondary: #10B981;
--secondary-light: #34D399;
--secondary-dark: #059669;

/* Accent Colors */
--accent: #F59E0B;
--accent-light: #FBBF24;
--accent-dark: #D97706;
```

### ألوان الحالة
```css
/* Status Colors */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
--info: #3B82F6;

/* Medical Specific */
--health-green: #22C55E;
--medical-blue: #0EA5E9;
--emergency-red: #DC2626;
--prescription-purple: #8B5CF6;
```

### ألوان النص
```css
/* Text Colors */
--text-primary: #1F2937;
--text-secondary: #6B7280;
--text-tertiary: #9CA3AF;
--text-inverse: #FFFFFF;
```

## 📐 نظام التخطيط

### الشبكة (Grid System)
```css
/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* Grid */
.grid {
  display: grid;
  gap: 16px;
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }
```

### المسافات (Spacing)
```css
/* Spacing Scale */
--space-xs: 4px;
--space-sm: 8px;
--space-md: 16px;
--space-lg: 24px;
--space-xl: 32px;
--space-2xl: 48px;
--space-3xl: 64px;
```

### الحواف المدورة (Border Radius)
```css
/* Border Radius */
--radius-sm: 4px;
--radius-md: 8px;
--radius-lg: 12px;
--radius-xl: 16px;
--radius-2xl: 24px;
--radius-full: 9999px;
```

## 🔤 نظام الخطوط

### الخطوط العربية
```css
/* Arabic Fonts */
--font-arabic: 'Cairo', sans-serif;
--font-arabic-light: 300;
--font-arabic-regular: 400;
--font-arabic-medium: 500;
--font-arabic-bold: 700;
```

### أحجام الخطوط
```css
/* Font Sizes */
--text-xs: 12px;
--text-sm: 14px;
--text-base: 16px;
--text-lg: 18px;
--text-xl: 20px;
--text-2xl: 24px;
--text-3xl: 30px;
--text-4xl: 36px;
```

## 🎭 المكونات (Components)

### الأزرار (Buttons)
```css
/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: var(--text-inverse);
  padding: 12px 24px;
  border-radius: var(--radius-lg);
  border: none;
  font-weight: var(--font-arabic-medium);
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}
```

### البطاقات (Cards)
```css
/* Medical Card */
.medical-card {
  background: var(--surface);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.medical-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}
```

### الحقول (Input Fields)
```css
/* Input Field */
.input-field {
  border: 2px solid var(--text-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  font-size: var(--text-base);
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}
```

## 📱 التصميم المتجاوب (Responsive Design)

### نقاط التوقف (Breakpoints)
```css
/* Mobile First */
@media (min-width: 640px) { /* Small */ }
@media (min-width: 768px) { /* Medium */ }
@media (min-width: 1024px) { /* Large */ }
@media (min-width: 1280px) { /* Extra Large */ }
```

### التخطيطات المتجاوبة
- **الهاتف**: عمود واحد
- **التابلت**: عمودين
- **الكمبيوتر**: 3-4 أعمدة
- **الشاشات الكبيرة**: 4+ أعمدة

## ♿ إمكانية الوصول (Accessibility)

### معايير WCAG 2.1
- **نسبة التباين**: 4.5:1 على الأقل
- **أحجام الخطوط**: قابلة للتكبير
- **التنقل**: بالكيبورد
- **القراءة الشاشية**: دعم كامل

### المميزات المساعدة
- **النصوص البديلة**: للصور
- **التسميات**: للحقول
- **الألوان**: ليست الوحيدة للمعلومات
- **التركيز**: مؤشرات واضحة

## 🎨 الرسوم المتحركة (Animations)

### الانتقالات (Transitions)
```css
/* Smooth Transitions */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### الرسوم المتحركة (Animations)
```css
/* Fade In */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Slide In */
@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}
```

## 📊 اختبار المستخدم (User Testing)

### طرق الاختبار
1. **اختبار قابلية الاستخدام**: مع مستخدمين حقيقيين
2. **اختبار A/B**: مقارنة تصميمات مختلفة
3. **تحليل السلوك**: تتبع حركة المستخدم
4. **استطلاعات الرأي**: جمع الملاحظات

### معايير النجاح
- **وقت الإنجاز**: أقل من 3 دقائق للحجز
- **معدل الخطأ**: أقل من 5%
- **رضا المستخدم**: 4.5/5 على الأقل
- **معدل الإكمال**: 90% على الأقل

## 🔄 التطوير المستمر

### مراجعة التصميم
- **مراجعة أسبوعية**: للتصميمات الجديدة
- **مراجعة شهرية**: لتحسينات UX
- **مراجعة ربع سنوية**: لتحديث الهوية البصرية

### التحديثات
- **التحديثات الصغيرة**: كل أسبوع
- **التحديثات الكبيرة**: كل شهر
- **إعادة التصميم**: كل 6 أشهر

---

**MediCare Design System** - تصميم طبي احترافي 🎨 