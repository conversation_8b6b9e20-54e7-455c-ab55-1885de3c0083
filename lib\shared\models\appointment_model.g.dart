// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentModel _$AppointmentModelFromJson(Map<String, dynamic> json) =>
    AppointmentModel(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      doctorId: json['doctorId'] as String,
      patientName: json['patientName'] as String?,
      doctorName: json['doctorName'] as String?,
      doctorSpecialization: json['doctorSpecialization'] as String?,
      appointmentDate: DateTime.parse(json['appointmentDate'] as String),
      appointmentTime: json['appointmentTime'] as String,
      duration: (json['duration'] as num?)?.toInt() ?? 30,
      appointmentType: json['appointmentType'] as String,
      status: json['status'] as String,
      reason: json['reason'] as String?,
      symptoms: json['symptoms'] as String?,
      notes: json['notes'] as String?,
      prescription: json['prescription'] as String?,
      diagnosis: json['diagnosis'] as String?,
      treatment: json['treatment'] as String?,
      recommendations: json['recommendations'] as String?,
      consultationFee: (json['consultationFee'] as num?)?.toDouble(),
      paymentMethod: json['paymentMethod'] as String? ?? 'card',
      paymentStatus: json['paymentStatus'] as String? ?? 'pending',
      paymentId: json['paymentId'] as String?,
      meetingLink: json['meetingLink'] as String?,
      meetingPassword: json['meetingPassword'] as String?,
      isOnline: json['isOnline'] as bool? ?? false,
      isEmergency: json['isEmergency'] as bool? ?? false,
      isFollowUp: json['isFollowUp'] as bool? ?? false,
      previousAppointmentId: json['previousAppointmentId'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      vitalSigns: json['vitalSigns'] as Map<String, dynamic>?,
      rating: json['rating'] as String?,
      review: json['review'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      cancelledAt: json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
      cancelledBy: json['cancelledBy'] as String?,
      cancelReason: json['cancelReason'] as String?,
    );

Map<String, dynamic> _$AppointmentModelToJson(AppointmentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'patientId': instance.patientId,
      'doctorId': instance.doctorId,
      'patientName': instance.patientName,
      'doctorName': instance.doctorName,
      'doctorSpecialization': instance.doctorSpecialization,
      'appointmentDate': instance.appointmentDate.toIso8601String(),
      'appointmentTime': instance.appointmentTime,
      'duration': instance.duration,
      'appointmentType': instance.appointmentType,
      'status': instance.status,
      'reason': instance.reason,
      'symptoms': instance.symptoms,
      'notes': instance.notes,
      'prescription': instance.prescription,
      'diagnosis': instance.diagnosis,
      'treatment': instance.treatment,
      'recommendations': instance.recommendations,
      'consultationFee': instance.consultationFee,
      'paymentMethod': instance.paymentMethod,
      'paymentStatus': instance.paymentStatus,
      'paymentId': instance.paymentId,
      'meetingLink': instance.meetingLink,
      'meetingPassword': instance.meetingPassword,
      'isOnline': instance.isOnline,
      'isEmergency': instance.isEmergency,
      'isFollowUp': instance.isFollowUp,
      'previousAppointmentId': instance.previousAppointmentId,
      'attachments': instance.attachments,
      'vitalSigns': instance.vitalSigns,
      'rating': instance.rating,
      'review': instance.review,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'cancelledAt': instance.cancelledAt?.toIso8601String(),
      'cancelledBy': instance.cancelledBy,
      'cancelReason': instance.cancelReason,
    };
