// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'doctor_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DoctorModel _$DoctorModelFromJson(Map<String, dynamic> json) => DoctorModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      specialization: json['specialization'] as String,
      specializations: (json['specializations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      licenseNumber: json['licenseNumber'] as String,
      licenseImageUrl: json['licenseImageUrl'] as String?,
      medicalSchool: json['medicalSchool'] as String?,
      yearsOfExperience: (json['yearsOfExperience'] as num).toInt(),
      bio: json['bio'] as String?,
      education: json['education'] as String?,
      certifications: (json['certifications'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      consultationFee: json['consultationFee'] as String?,
      followUpFee: json['followUpFee'] as String?,
      emergencyFee: json['emergencyFee'] as String?,
      workingDays: (json['workingDays'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      workingHours: json['workingHours'] as String?,
      timezone: json['timezone'] as String?,
      isAvailable: json['isAvailable'] as bool? ?? true,
      isVerified: json['isVerified'] as bool? ?? false,
      isOnline: json['isOnline'] as bool? ?? false,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
      totalPatients: (json['totalPatients'] as num?)?.toInt() ?? 0,
      totalAppointments: (json['totalAppointments'] as num?)?.toInt() ?? 0,
      insuranceProviders: (json['insuranceProviders'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      acceptedPaymentMethods: (json['acceptedPaymentMethods'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      availability: json['availability'] as Map<String, dynamic>?,
      services: (json['services'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      clinicAddress: json['clinicAddress'] as String?,
      clinicPhone: json['clinicPhone'] as String?,
      clinicEmail: json['clinicEmail'] as String?,
      website: json['website'] as String?,
      socialMedia: (json['socialMedia'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DoctorModelToJson(DoctorModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'specialization': instance.specialization,
      'specializations': instance.specializations,
      'licenseNumber': instance.licenseNumber,
      'licenseImageUrl': instance.licenseImageUrl,
      'medicalSchool': instance.medicalSchool,
      'yearsOfExperience': instance.yearsOfExperience,
      'bio': instance.bio,
      'education': instance.education,
      'certifications': instance.certifications,
      'languages': instance.languages,
      'consultationFee': instance.consultationFee,
      'followUpFee': instance.followUpFee,
      'emergencyFee': instance.emergencyFee,
      'workingDays': instance.workingDays,
      'workingHours': instance.workingHours,
      'timezone': instance.timezone,
      'isAvailable': instance.isAvailable,
      'isVerified': instance.isVerified,
      'isOnline': instance.isOnline,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'totalPatients': instance.totalPatients,
      'totalAppointments': instance.totalAppointments,
      'insuranceProviders': instance.insuranceProviders,
      'acceptedPaymentMethods': instance.acceptedPaymentMethods,
      'availability': instance.availability,
      'services': instance.services,
      'clinicAddress': instance.clinicAddress,
      'clinicPhone': instance.clinicPhone,
      'clinicEmail': instance.clinicEmail,
      'website': instance.website,
      'socialMedia': instance.socialMedia,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
