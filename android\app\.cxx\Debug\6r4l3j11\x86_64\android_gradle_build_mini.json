{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter_app1\\android\\app\\.cxx\\Debug\\6r4l3j11\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter_app1\\android\\app\\.cxx\\Debug\\6r4l3j11\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}