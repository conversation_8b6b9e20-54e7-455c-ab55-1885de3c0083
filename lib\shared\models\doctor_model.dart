import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'doctor_model.g.dart';

@JsonSerializable()
class DoctorModel extends Equatable {
  final String id;
  final String userId;
  final String specialization;
  final List<String> specializations;
  final String licenseNumber;
  final String? licenseImageUrl;
  final String? medicalSchool;
  final int yearsOfExperience;
  final String? bio;
  final String? education;
  final List<String>? certifications;
  final List<String>? languages;
  final String? consultationFee;
  final String? followUpFee;
  final String? emergencyFee;
  final List<String>? workingDays;
  final String? workingHours;
  final String? timezone;
  final bool isAvailable;
  final bool isVerified;
  final bool isOnline;
  final double rating;
  final int totalReviews;
  final int totalPatients;
  final int totalAppointments;
  final List<String>? insuranceProviders;
  final List<String>? acceptedPaymentMethods;
  final Map<String, dynamic>? availability;
  final List<String>? services;
  final String? clinicAddress;
  final String? clinicPhone;
  final String? clinicEmail;
  final String? website;
  final List<String>? socialMedia;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DoctorModel({
    required this.id,
    required this.userId,
    required this.specialization,
    required this.specializations,
    required this.licenseNumber,
    this.licenseImageUrl,
    this.medicalSchool,
    required this.yearsOfExperience,
    this.bio,
    this.education,
    this.certifications,
    this.languages,
    this.consultationFee,
    this.followUpFee,
    this.emergencyFee,
    this.workingDays,
    this.workingHours,
    this.timezone,
    this.isAvailable = true,
    this.isVerified = false,
    this.isOnline = false,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.totalPatients = 0,
    this.totalAppointments = 0,
    this.insuranceProviders,
    this.acceptedPaymentMethods,
    this.availability,
    this.services,
    this.clinicAddress,
    this.clinicPhone,
    this.clinicEmail,
    this.website,
    this.socialMedia,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DoctorModel.fromJson(Map<String, dynamic> json) => _$DoctorModelFromJson(json);
  Map<String, dynamic> toJson() => _$DoctorModelToJson(this);

  String get displayName => 'Dr. $specialization';
  
  String get experienceText {
    if (yearsOfExperience == 1) return '1 year';
    return '$yearsOfExperience years';
  }

  String get ratingText => '${rating.toStringAsFixed(1)} ($totalReviews reviews)';

  bool get isWorkingToday {
    if (workingDays == null) return false;
    final today = DateTime.now().weekday;
    final dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return workingDays!.contains(dayNames[today - 1]);
  }

  List<String> get allSpecializations {
    final all = [specialization, ...specializations];
    return all.toSet().toList();
  }

  DoctorModel copyWith({
    String? id,
    String? userId,
    String? specialization,
    List<String>? specializations,
    String? licenseNumber,
    String? licenseImageUrl,
    String? medicalSchool,
    int? yearsOfExperience,
    String? bio,
    String? education,
    List<String>? certifications,
    List<String>? languages,
    String? consultationFee,
    String? followUpFee,
    String? emergencyFee,
    List<String>? workingDays,
    String? workingHours,
    String? timezone,
    bool? isAvailable,
    bool? isVerified,
    bool? isOnline,
    double? rating,
    int? totalReviews,
    int? totalPatients,
    int? totalAppointments,
    List<String>? insuranceProviders,
    List<String>? acceptedPaymentMethods,
    Map<String, dynamic>? availability,
    List<String>? services,
    String? clinicAddress,
    String? clinicPhone,
    String? clinicEmail,
    String? website,
    List<String>? socialMedia,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DoctorModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      specialization: specialization ?? this.specialization,
      specializations: specializations ?? this.specializations,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      licenseImageUrl: licenseImageUrl ?? this.licenseImageUrl,
      medicalSchool: medicalSchool ?? this.medicalSchool,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      bio: bio ?? this.bio,
      education: education ?? this.education,
      certifications: certifications ?? this.certifications,
      languages: languages ?? this.languages,
      consultationFee: consultationFee ?? this.consultationFee,
      followUpFee: followUpFee ?? this.followUpFee,
      emergencyFee: emergencyFee ?? this.emergencyFee,
      workingDays: workingDays ?? this.workingDays,
      workingHours: workingHours ?? this.workingHours,
      timezone: timezone ?? this.timezone,
      isAvailable: isAvailable ?? this.isAvailable,
      isVerified: isVerified ?? this.isVerified,
      isOnline: isOnline ?? this.isOnline,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      totalPatients: totalPatients ?? this.totalPatients,
      totalAppointments: totalAppointments ?? this.totalAppointments,
      insuranceProviders: insuranceProviders ?? this.insuranceProviders,
      acceptedPaymentMethods: acceptedPaymentMethods ?? this.acceptedPaymentMethods,
      availability: availability ?? this.availability,
      services: services ?? this.services,
      clinicAddress: clinicAddress ?? this.clinicAddress,
      clinicPhone: clinicPhone ?? this.clinicPhone,
      clinicEmail: clinicEmail ?? this.clinicEmail,
      website: website ?? this.website,
      socialMedia: socialMedia ?? this.socialMedia,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    specialization,
    specializations,
    licenseNumber,
    licenseImageUrl,
    medicalSchool,
    yearsOfExperience,
    bio,
    education,
    certifications,
    languages,
    consultationFee,
    followUpFee,
    emergencyFee,
    workingDays,
    workingHours,
    timezone,
    isAvailable,
    isVerified,
    isOnline,
    rating,
    totalReviews,
    totalPatients,
    totalAppointments,
    insuranceProviders,
    acceptedPaymentMethods,
    availability,
    services,
    clinicAddress,
    clinicPhone,
    clinicEmail,
    website,
    socialMedia,
    createdAt,
    updatedAt,
  ];
} 