import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../shared/widgets/medical_card.dart';
import '../../../../shared/widgets/doctor_card.dart';
import '../../../../shared/widgets/appointment_card.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/models/appointment_model.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildBody(),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً بك في',
                    style: TextStyle(
                      color: AppColors.withOpacity(AppColors.textInverse, 0.8),
                      fontSize: 16,
                    ),
                  ),
                  const Text(
                    'MediCare',
                    style: TextStyle(
                      color: AppColors.textInverse,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              CircleAvatar(
                radius: 20,
                backgroundColor: AppColors.textInverse,
                child: Icon(
                  Icons.person,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.withOpacity(AppColors.textInverse, 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.search,
                  color: AppColors.textInverse,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'البحث عن طبيب أو تخصص...',
                  style: TextStyle(
                    color: AppColors.withOpacity(AppColors.textInverse, 0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboard();
      case 1:
        return _buildAppointments();
      case 2:
        return _buildDoctors();
      case 3:
        return _buildProfile();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildUpcomingAppointments(),
          const SizedBox(height: 24),
          _buildRecommendedDoctors(),
          const SizedBox(height: 24),
          _buildMedicalServices(),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الخدمات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            MedicalCard(
              title: 'حجز موعد',
              subtitle: 'احجز موعد مع طبيب',
              icon: Icons.calendar_today,
              color: AppColors.primary,
              onTap: () {
                // Navigate to appointment booking
              },
            ),
            MedicalCard(
              title: 'فحص الأعراض',
              subtitle: 'تحقق من أعراضك',
              icon: Icons.medical_services,
              color: AppColors.secondary,
              onTap: () {
                // Navigate to symptom checker
              },
            ),
            MedicalCard(
              title: 'الموسوعة الطبية',
              subtitle: 'مقالات ونصائح طبية',
              icon: Icons.library_books,
              color: AppColors.accent,
              onTap: () {
                // Navigate to medical encyclopedia
              },
            ),
            MedicalCard(
              title: 'الدردشة الطبية',
              subtitle: 'تحدث مع طبيب',
              icon: Icons.chat,
              color: AppColors.prescriptionPurple,
              onTap: () {
                // Navigate to medical chat
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUpcomingAppointments() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'المواعيد القادمة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedIndex = 1;
                });
              },
              child: const Text(
                'عرض الكل',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Mock data - replace with real data
        AppointmentCard(
          appointment: AppointmentModel(
            id: '1',
            patientId: 'patient1',
            doctorId: 'doctor1',
            patientName: 'أحمد محمد',
            doctorName: 'د. سارة أحمد',
            doctorSpecialization: 'طب القلب',
            appointmentDate: DateTime.now().add(const Duration(days: 2)),
            appointmentTime: '14:30',
            appointmentType: 'consultation',
            status: 'confirmed',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          onTap: () {
            // Navigate to appointment details
          },
        ),
      ],
    );
  }

  Widget _buildRecommendedDoctors() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الأطباء الموصى بهم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedIndex = 2;
                });
              },
              child: const Text(
                'عرض الكل',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 16),
                child: DoctorCard(
                  doctor: DoctorModel(
                    id: 'doctor$index',
                    userId: 'user$index',
                    specialization: 'طب القلب',
                    specializations: ['طب القلب', 'طب الأوعية الدموية'],
                    licenseNumber: 'LIC${index + 1}',
                    yearsOfExperience: 5 + index,
                    rating: 4.5 + (index * 0.1),
                    totalReviews: 50 + (index * 10),
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  ),
                  onTap: () {
                    // Navigate to doctor profile
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMedicalServices() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الخدمات الطبية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildServiceItem(
                icon: Icons.emergency,
                title: 'الطوارئ الطبية',
                subtitle: 'اتصل بنا في حالة الطوارئ',
                color: AppColors.emergencyRed,
              ),
              const Divider(),
              _buildServiceItem(
                icon: Icons.medication,
                title: 'تذكير الأدوية',
                subtitle: 'لا تنس تناول أدويتك',
                color: AppColors.healthGreen,
              ),
              const Divider(),
              _buildServiceItem(
                icon: Icons.file_copy,
                title: 'السجلات الطبية',
                subtitle: 'احتفظ بسجلاتك الطبية',
                color: AppColors.medicalBlue,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildServiceItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.withOpacity(color, 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: color,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: AppColors.textTertiary,
        size: 16,
      ),
      onTap: () {
        // Navigate to service
      },
    );
  }

  Widget _buildAppointments() {
    return const Center(
      child: Text(
        'صفحة المواعيد',
        style: TextStyle(fontSize: 20),
      ),
    );
  }

  Widget _buildDoctors() {
    return const Center(
      child: Text(
        'صفحة الأطباء',
        style: TextStyle(fontSize: 20),
      ),
    );
  }

  Widget _buildProfile() {
    return const Center(
      child: Text(
        'صفحة الملف الشخصي',
        style: TextStyle(fontSize: 20),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textTertiary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'المواعيد',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.medical_services),
            label: 'الأطباء',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الملف الشخصي',
          ),
        ],
      ),
    );
  }
} 